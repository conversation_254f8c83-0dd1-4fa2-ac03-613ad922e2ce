
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Dictionary } from "../../lib/get-translation";
import { AiOutlineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { z } from 'zod'; // Import zod
import Button from "./varients/button";

interface LoginPageProps {
  dictionary: Dictionary;
  lang: string;
}

// Define validation schema using dictionary for messages
const getLoginSchema = (dictionary: Dictionary) => z.object({
  email: z.string().email(dictionary.auth.login.validation?.invalid_email || "Invalid email format"),
  password: z.string().min(8, dictionary.auth.login.validation?.password_length || "Password must be at least 8 characters")
});

export default function LoginPage({ dictionary, lang }: LoginPageProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState("");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const loginSchema = getLoginSchema(dictionary);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setApiError("");
    setFormErrors({});

    const result = loginSchema.safeParse(formData);

    if (!result.success) {
      const errors: Record<string, string> = {};
      result.error.issues.forEach(issue => {
        errors[issue.path[0]] = issue.message;
      });
      setFormErrors(errors);
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          device_token: "web-app",
          device_type: "web",
        }),
      });

      if (!response.ok) {
        throw new Error(dictionary.auth.login.invalid_credentials);
      }

      const data = await response.json();

      localStorage.setItem("auth_token", data.token);
      localStorage.setItem("user_data", JSON.stringify(data.user));

      const dashboardPath = lang === "fr" ? "/fr/tableau-de-bord" : "/dashboard";
      router.push(dashboardPath);
    } catch (err) {
      setApiError(err instanceof Error ? err.message : dictionary.auth.login.error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear errors on change
    if (formErrors[e.target.name]) {
      setFormErrors({
        ...formErrors,
        [e.target.name]: "",
      });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Container with Figma grid specifications */}
      <div className="mx-4 lg:mx-[120px] py-8 lg:py-16">
        {/* Main Content Wrapper */}
        <div className="mt-8 lg:mt-16 mb-8 lg:mb-16">
          <div className="grid grid-cols-4 lg:grid-cols-12 gap-4 lg:gap-5 items-start">
            {/* Left Side - Form (Mobile: 4 cols, Desktop: 6 cols) */}
            <div className="col-span-4 lg:col-span-6 space-y-6 lg:space-y-12">
              {/* Title */}
              <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl text-black text-center lg:text-left font-prata">
                {dictionary.auth.login.title}
              </h1>

              {/* Login Form */}
              <form onSubmit={handleSubmit} className="space-y-6 lg:space-y-8" noValidate>
                {/* Email Field */}
                <div>
                  <label htmlFor="email" className="block text-sm sm:text-base text-black mb-2 font-karla">
                    {dictionary.auth.login.email} <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    className="w-full px-0 py-2 sm:py-3 bg-transparent border-0 border-b border-gray-300 focus:outline-none focus:border-black text-black placeholder-gray-400 text-sm sm:text-base"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                  {formErrors.email && (
                    <p className="text-red-500 text-xs sm:text-sm mt-1">{formErrors.email}</p>
                  )}
                </div>

                {/* Password Field */}
                <div>
                  <label htmlFor="password" className="block text-sm sm:text-base text-black mb-2 font-karla">
                    {dictionary.auth.login.password} <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      autoComplete="current-password"
                      className="w-full px-0 py-2 sm:py-3 bg-transparent border-0 border-b border-gray-300 focus:outline-none focus:border-black text-black placeholder-gray-400 pr-10 text-sm sm:text-base"
                      value={formData.password}
                      onChange={handleInputChange}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-0 top-2 sm:top-3 text-gray-500 hover:text-black flex-shrink-0"
                    >
                      {showPassword ? (
                        <AiOutlineEyeInvisible className="h-4 w-4 sm:h-5 sm:w-5" />
                      ) : (
                        <AiOutlineEye className="h-4 w-4 sm:h-5 sm:w-5" />
                      )}
                    </button>
                  </div>
                  {formErrors.password && (
                    <p className="text-red-500 text-xs sm:text-sm mt-1">{formErrors.password}</p>
                  )}
                </div>

                {/* Remember Me & Forgot Password */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-yellow-600 focus:ring-yellow-500 accent-yellow-600 flex-shrink-0"
                    />
                    <label htmlFor="remember-me" className="ml-2 text-sm sm:text-base text-black font-karla">
                      {dictionary.auth.login.remember_me}
                    </label>
                  </div>
                  <a href="#" className="text-sm sm:text-base text-yellow-600 hover:text-yellow-500 font-karla">
                    {dictionary.auth.login.forgot_password}
                  </a>
                </div>

                {/* API Error */}
                {apiError && (
                  <div className="text-red-600 text-sm sm:text-base bg-red-50 p-3 rounded-md">
                    {apiError}
                  </div>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  variant="auth"
                  width="w-full"
                >
                  {isLoading ? dictionary.auth.login.submitting : dictionary.auth.login.submit}
                </Button>

                {/* Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300"></div>
                  </div>
                  <div className="relative flex justify-center text-sm sm:text-base">
                    <span className="px-4 sm:px-6 bg-white text-black font-karla">
                      {dictionary.auth.login.or}
                    </span>
                  </div>
                </div>

                {/* Social Login Buttons */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <button
                    type="button"
                    className="flex items-center justify-center py-3 sm:py-4 px-4 border border-gray-300 bg-white text-sm sm:text-base font-medium hover:bg-gray-50 hover:border-black transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 48 48" className="mr-2 sm:mr-3 flex-shrink-0">
                      <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.574l6.19,5.238C42.021,35.596,44,30.138,44,24C44,22.659,43.862,21.35,43.611,20.083z"></path>
                    </svg>
                    <span className="font-karla truncate">Google</span>
                  </button>
                  <button
                    type="button"
                    className="flex items-center justify-center py-3 sm:py-4 px-4 border border-gray-300 bg-white text-sm sm:text-base font-medium hover:bg-gray-50 hover:border-black transition-colors"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 48 48" className="mr-2 sm:mr-3 flex-shrink-0">
                      <path fill="#ff5722" d="M6 6H22V22H6z" transform="rotate(-180 14 14)"></path><path fill="#4caf50" d="M26 6H42V22H26z" transform="rotate(-180 34 14)"></path><path fill="#ffc107" d="M26 26H42V42H26z" transform="rotate(-180 34 34)"></path><path fill="#03a9f4" d="M6 26H22V42H6z" transform="rotate(-180 14 34)"></path>
                    </svg>
                    <span className="font-karla truncate">Microsoft</span>
                  </button>
                </div>

                {/* Sign Up Link */}
                <div className="text-center">
                  <p className="text-sm sm:text-base text-black font-karla">
                    {dictionary.auth.login.no_account}{' '}
                    <a
                      href="/register"
                      className="text-yellow-600 underline underline-offset-4 hover:text-yellow-500"
                    >
                      {dictionary.auth.login.sign_up}
                    </a>
                  </p>
                </div>
              </form>
            </div>

            {/* Right Side - Image (Desktop: 6 cols, Hidden on mobile) */}
            <div className="hidden lg:flex lg:col-span-6 items-center justify-center">
              <div className="relative">
                <Image
                  src="/dollar.png"
                  alt={dictionary.auth.login.image_alt}
                  width={500}
                  height={600}
                  className="object-contain max-w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

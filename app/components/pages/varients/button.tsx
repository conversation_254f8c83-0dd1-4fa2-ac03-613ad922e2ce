import React from "react";
import { clsx } from "clsx";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "primary" | "auth";
  width?: string;
  className?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, variant = "primary", width, className, ...props }, ref) => {
    const baseClasses = "text-white font-medium transition-colors focus:outline-none disabled:opacity-50 font-karla";

    const variants = {
      primary: "flex items-center justify-center px-8 xl:px-10 py-2 xl:py-3 bg-[var(--color-primary)] hover:bg-[#a7813d] text-base xl:text-lg whitespace-nowrap uppercase",
      auth: "py-3 sm:py-4 bg-black hover:bg-[var(--color-primary)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300 ease-in-out text-base sm:text-lg"
    };

    return (
      <button
        ref={ref}
        className={clsx(
          baseClasses,
          variants[variant],
          width,
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;

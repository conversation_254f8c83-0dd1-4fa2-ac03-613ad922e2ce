@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Karla:wght@400;700&family=Prata:wght@400&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-karla: 'Karla', sans-serif;
  --font-prata: 'Prata', serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

:root {
  --color-primary: #b9924f;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overscroll-behavior-y: none;
}

/* Font utility classes */
.font-karla {
  font-family: var(--font-karla);
}

.font-prata {
  font-family: var(--font-prata);
}

/* Loading animation styles */
@keyframes loading {
  0% {
    width: 0%;
    background-position: 0% 50%;
  }

  50% {
    width: 70%;
    background-position: 100% 50%;
  }

  100% {
    width: 100%;
    background-position: 0% 50%;
  }
}

.animate-loading-bar {
  animation: loading 2s ease-in-out infinite;
  background: linear-gradient(90deg, rgba(185, 146, 79, 0.8) 0%, rgba(185, 146, 79, 1) 50%, rgba(185, 146, 79, 0.8) 100%);
  background-size: 200% 200%;
}